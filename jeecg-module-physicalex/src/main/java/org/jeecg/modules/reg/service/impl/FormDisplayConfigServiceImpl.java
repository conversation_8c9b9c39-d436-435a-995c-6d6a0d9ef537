package org.jeecg.modules.reg.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.reg.entity.FieldDisplayConfig;
import org.jeecg.modules.reg.entity.FormDisplayConfig;
import org.jeecg.modules.reg.mapper.FieldDisplayConfigMapper;
import org.jeecg.modules.reg.mapper.FormDisplayConfigMapper;
import org.jeecg.modules.reg.service.IFormDisplayConfigService;
import org.jeecg.modules.reg.vo.FormDisplayConfigQueryParam;
import org.jeecg.modules.reg.vo.ConfigValidationResult;
import org.jeecg.modules.reg.vo.FieldConfigRequest;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 表单显示配置Service实现
 */
@Slf4j
@Service
@Transactional
public class FormDisplayConfigServiceImpl extends ServiceImpl<FormDisplayConfigMapper, FormDisplayConfig>
        implements IFormDisplayConfigService {

    @Autowired
    private FieldDisplayConfigMapper fieldMapper;

    @Override
    public IPage<FormDisplayConfig> getConfigList(Page<FormDisplayConfig> page, FormDisplayConfigQueryParam param) {
        QueryWrapper<FormDisplayConfig> queryWrapper = new QueryWrapper<>();

        if (StrUtil.isNotBlank(param.getConfigName())) {
            queryWrapper.like("config_name", param.getConfigName());
        }
        if (StrUtil.isNotBlank(param.getFormType())) {
            queryWrapper.eq("form_type", param.getFormType());
        }
        if (param.getIsActive() != null) {
            queryWrapper.eq("is_active", param.getIsActive());
        }
        if (StrUtil.isNotBlank(param.getCenterId())) {
            queryWrapper.eq("center_id", param.getCenterId());
        }

        queryWrapper.orderByDesc("create_time");
        return this.page(page, queryWrapper);
    }

    @Override
    public FormDisplayConfig getConfigWithFields(String id) {
        FormDisplayConfig config = this.getById(id);
        if (config != null) {
            List<FieldDisplayConfig> fields = fieldMapper.selectList(
                    new QueryWrapper<FieldDisplayConfig>().eq("config_id", id)
            );
            config.setFields(fields);
        }
        return config;
    }

    @Override
    public boolean saveConfigWithFields(FormDisplayConfig config) {
        try {
            // 设置默认值
            if (StrUtil.isBlank(config.getCenterId())) {
                config.setCenterId("default_center");
                config.setCenterName("默认体检中心");
            }

            // 先查询是否已存在
            QueryWrapper<FormDisplayConfig> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("center_id", config.getCenterId())
                    .eq("form_type", config.getFormType());
            FormDisplayConfig existingConfig = this.getOne(queryWrapper);

            if (existingConfig != null) {
                // 更新现有配置
                config.setId(existingConfig.getId());
                // 删除原有字段配置
                fieldMapper.deleteByConfigId(config.getId());
            }
            // 使用 saveOrUpdate 方法
            this.saveOrUpdate(config);

            // 保存字段配置
            if (CollUtil.isNotEmpty(config.getFields())) {
                for (FieldDisplayConfig field : config.getFields()) {
                    field.setConfigId(config.getId());
                    fieldMapper.insert(field);
                }
                //fieldMapper.batchInsert(config.getFields());
            }

            return true;
        } catch (Exception e) {
            log.error("保存配置失败", e);
            throw new RuntimeException("保存配置失败: " + e.getMessage());
        }
    }

    @Override
    public FormDisplayConfig getActiveConfig(String formType) {
        return baseMapper.getActiveConfigWithFields(formType);
    }

    @Override
    public FormDisplayConfig getActiveConfig(String formType, String centerId) {
        QueryWrapper<FormDisplayConfig> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("form_type", formType)
                .eq("is_active", true);

        if (StrUtil.isNotBlank(centerId)) {
            queryWrapper.eq("center_id", centerId);
        }

        queryWrapper.orderByDesc("create_time").last("LIMIT 1");

        FormDisplayConfig config = this.getOne(queryWrapper);
        if (config != null) {
            List<FieldDisplayConfig> fields = fieldMapper.selectList(
                    new QueryWrapper<FieldDisplayConfig>().eq("config_id", config.getId())
            );
            config.setFields(fields);
        }

        return config;
    }

    @Override
    public FormDisplayConfig getDefaultConfig(String formType) {
        // 返回默认配置模板
        FormDisplayConfig defaultConfig = new FormDisplayConfig();
        defaultConfig.setConfigName("默认配置");
        defaultConfig.setFormType(formType);
        defaultConfig.setIsActive(true);

        // 根据表单类型返回不同的默认字段配置
        List<FieldDisplayConfig> defaultFields = getDefaultFieldsByFormType(formType);
        defaultConfig.setFields(defaultFields);

        return defaultConfig;
    }

    private List<FieldDisplayConfig> getDefaultFieldsByFormType(String formType) {
        List<FieldDisplayConfig> fields = new ArrayList<>();

        if ("customer_reg".equals(formType)) {
            // 客户登记表单默认字段
            fields.add(createField("nation", "民族", true, "基础信息"));
            fields.add(createField("bloodType", "血型", true, "基础信息"));
            fields.add(createField("countryCode", "国籍", true, "基础信息"));
            fields.add(createField("postCode", "邮政编码", false, "基础信息"));
            fields.add(createField("eduLevel", "文化程度", true, "基础信息"));
            fields.add(createField("marriageStatus", "婚姻状况", false, "基础信息"));
            fields.add(createField("customerCategory", "客户类别", false, "基础信息"));
            fields.add(createField("pcaCode", "省市区县", false, "地址信息"));
            fields.add(createField("address", "详细地址", false, "地址信息"));
            fields.add(createField("emergencyContact", "紧急联系人", false, "联系信息"));
            fields.add(createField("emergencyPhone", "紧急电话", false, "联系信息"));
            fields.add(createField("isPregnancyPrep", "是否备孕", false, "健康信息"));
            fields.add(createField("healthNo", "健康证号", false, "证件信息"));
            fields.add(createField("examCardNo", "体检卡号", false, "证件信息"));
            fields.add(createField("workYears", "工龄", false, "职业信息"));
            fields.add(createField("companyName", "单位名称", false, "单位信息"));
            fields.add(createField("belongCompany", "所属单位", false, "单位信息"));
            fields.add(createField("department", "所属科室", false, "单位信息"));
            fields.add(createField("supplyFlag", "补检", true, "标识信息"));
            fields.add(createField("prePayFlag", "预缴", true, "标识信息"));
            fields.add(createField("reExamStatus", "是否复查", true, "标识信息"));
            fields.add(createField("reExamRemark", "复查备注", true, "标识信息"));
            fields.add(createField("recipeTitle", "发票抬头", false, "财务信息"));
            fields.add(createField("originalIdCard", "原检证件号", false, "原检信息"));
            fields.add(createField("relationWithOriginal", "与原检关系", false, "原检信息"));
            fields.add(createField("introducer", "介绍人", false, "其他信息"));
            fields.add(createField("secretLevel", "保密等级", false, "其他信息"));
            fields.add(createField("remark", "备注", false, "其他信息"));
        }

        return fields;
    }

    private FieldDisplayConfig createField(String fieldKey, String fieldName, boolean isVisible, String groupName) {
        FieldDisplayConfig field = new FieldDisplayConfig();
        field.setFieldKey(fieldKey);
        field.setFieldName(fieldName);
        field.setIsVisible(isVisible);
        field.setGroupName(groupName);
        return field;
    }

    // ==================== 新增方法实现 ====================

    @Override
    public ConfigValidationResult validateFieldConfig(FieldConfigRequest request) {
        List<String> errors = new ArrayList<>();
        List<String> warnings = new ArrayList<>();
        List<String> suggestions = new ArrayList<>();

        try {
            // 验证必填字段
            if (StrUtil.isBlank(request.getFieldKey())) {
                errors.add("字段键不能为空");
            }
            if (StrUtil.isBlank(request.getFieldName())) {
                errors.add("字段名称不能为空");
            }
            if (StrUtil.isBlank(request.getFormType())) {
                errors.add("表单类型不能为空");
            }

            // 验证字段键格式
            if (StrUtil.isNotBlank(request.getFieldKey())) {
                if (!request.getFieldKey().matches("^[a-zA-Z][a-zA-Z0-9_]*$")) {
                    errors.add("字段键格式不正确，应以字母开头，只包含字母、数字和下划线");
                }
            }

            // 验证显示位置
            if (StrUtil.isNotBlank(request.getDisplayLocation())) {
                if (!Arrays.asList("outside", "collapse", "hidden").contains(request.getDisplayLocation())) {
                    errors.add("显示位置值无效，应为 outside、collapse 或 hidden");
                }
            }

            // 验证排序号
            if (request.getSortOrder() != null && request.getSortOrder() < 0) {
                warnings.add("排序号建议设置为非负数");
            }

            // 提供建议
            if (StrUtil.isBlank(request.getGroupName())) {
                suggestions.add("建议设置分组名称以便更好地组织字段");
            }
            if (request.getSortOrder() == null) {
                suggestions.add("建议设置排序号以控制字段显示顺序");
            }

            ConfigValidationResult result = new ConfigValidationResult();
            result.setIsValid(errors.isEmpty());
            result.setErrors(errors);
            result.setWarnings(warnings);
            result.setSuggestions(suggestions);

            return result;
        } catch (Exception e) {
            log.error("验证字段配置时发生错误", e);
            return ConfigValidationResult.error(Arrays.asList("验证过程中发生错误: " + e.getMessage()));
        }
    }

    @Override
    public List<FormDisplayConfig> getConfigVersions(String formType, String centerId) {
        QueryWrapper<FormDisplayConfig> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("form_type", formType);

        if (StrUtil.isNotBlank(centerId)) {
            queryWrapper.eq("center_id", centerId);
        }

        queryWrapper.orderByDesc("create_time");

        return this.list(queryWrapper);
    }

    @Override
    public String copyConfig(String sourceId, String newConfigName, String targetCenterId) {
        try {
            // 获取源配置
            FormDisplayConfig sourceConfig = this.getConfigWithFields(sourceId);
            if (sourceConfig == null) {
                throw new RuntimeException("源配置不存在");
            }

            // 创建新配置
            FormDisplayConfig newConfig = new FormDisplayConfig();
            BeanUtils.copyProperties(sourceConfig, newConfig);
            newConfig.setId(null); // 清除ID，让系统自动生成
            newConfig.setConfigName(newConfigName);
            newConfig.setIsActive(false); // 新配置默认不激活

            if (StrUtil.isNotBlank(targetCenterId)) {
                newConfig.setCenterId(targetCenterId);
            }

            // 复制字段配置
            List<FieldDisplayConfig> newFields = new ArrayList<>();
            if (CollUtil.isNotEmpty(sourceConfig.getFields())) {
                for (FieldDisplayConfig field : sourceConfig.getFields()) {
                    FieldDisplayConfig newField = new FieldDisplayConfig();
                    BeanUtils.copyProperties(field, newField);
                    newField.setId(null); // 清除ID
                    newField.setConfigId(null); // 稍后设置
                    newFields.add(newField);
                }
            }
            newConfig.setFields(newFields);

            // 保存新配置
            boolean success = this.saveConfigWithFields(newConfig);
            if (success) {
                return newConfig.getId();
            } else {
                throw new RuntimeException("保存新配置失败");
            }
        } catch (Exception e) {
            log.error("复制配置失败", e);
            throw new RuntimeException("复制配置失败: " + e.getMessage());
        }
    }

    @Override
    public boolean activateConfig(String configId) {
        try {
            FormDisplayConfig config = this.getById(configId);
            if (config == null) {
                throw new RuntimeException("配置不存在");
            }

            // 先将同类型的其他配置设为非激活状态
            UpdateWrapper<FormDisplayConfig> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("form_type", config.getFormType())
                    .eq("center_id", config.getCenterId())
                    .set("is_active", false);
            this.update(updateWrapper);

            // 激活当前配置
            config.setIsActive(true);
            return this.updateById(config);
        } catch (Exception e) {
            log.error("激活配置失败", e);
            throw new RuntimeException("激活配置失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Boolean> getUserFieldConfigPermissions(LoginUser loginUser) {
        Map<String, Boolean> permissions = new HashMap<>();

        try {
            // 这里应该根据实际的权限系统来实现
            // 暂时提供一个基础实现

            boolean isAdmin = loginUser.getUserIdentity() != null &&
                    (loginUser.getUserIdentity() == 1 || loginUser.getUserIdentity() == 2);

            permissions.put("field:config:view", true); // 所有用户都可以查看
            permissions.put("field:config:edit", isAdmin);
            permissions.put("field:config:create", isAdmin);
            permissions.put("field:config:delete", isAdmin);
            permissions.put("form:config:manage", isAdmin);
            permissions.put("form:config:apply", true);

        } catch (Exception e) {
            log.error("获取用户权限失败", e);
            // 返回最小权限
            permissions.put("field:config:view", true);
            permissions.put("field:config:edit", false);
            permissions.put("field:config:create", false);
            permissions.put("field:config:delete", false);
            permissions.put("form:config:manage", false);
            permissions.put("form:config:apply", false);
        }

        return permissions;
    }

    @Override
    public boolean batchUpdateFieldConfigs(List<FieldConfigRequest> requests) {
        try {
            log.info("开始批量更新字段配置: requests.size={}", requests.size());

            if (CollUtil.isEmpty(requests)) {
                log.warn("批量更新字段配置失败：请求列表为空");
                return false;
            }

            // 按表单类型分组处理
            Map<String, List<FieldConfigRequest>> groupedRequests = requests.stream()
                .collect(Collectors.groupingBy(FieldConfigRequest::getFormType));

            for (Map.Entry<String, List<FieldConfigRequest>> entry : groupedRequests.entrySet()) {
                String formType = entry.getKey();
                List<FieldConfigRequest> formRequests = entry.getValue();

                // 查找或创建表单配置
                QueryWrapper<FormDisplayConfig> wrapper = new QueryWrapper<>();
                wrapper.eq("form_type", formType);
                FormDisplayConfig formConfig = this.getOne(wrapper);

                if (formConfig == null) {
                    // 创建新的表单配置
                    formConfig = new FormDisplayConfig();
                    formConfig.setConfigName("默认配置-" + formType);
                    formConfig.setFormType(formType);
                    formConfig.setCenterId("default");
                    formConfig.setCenterName("默认体检中心");
                    formConfig.setIsActive(true);
                    formConfig.setCreateTime(new Date());
                    formConfig.setUpdateTime(new Date());
                    this.save(formConfig);
                }

                // 更新字段配置
                for (FieldConfigRequest request : formRequests) {
                    // 查找现有字段配置
                    QueryWrapper<FieldDisplayConfig> fieldWrapper = new QueryWrapper<>();
                    fieldWrapper.eq("config_id", formConfig.getId())
                               .eq("field_key", request.getFieldKey());

                    FieldDisplayConfig fieldConfig = fieldMapper.selectOne(fieldWrapper);

                    if (fieldConfig == null) {
                        // 创建新字段配置
                        fieldConfig = new FieldDisplayConfig();
                        fieldConfig.setConfigId(formConfig.getId());
                        fieldConfig.setFieldKey(request.getFieldKey());
                        fieldConfig.setFieldName(request.getFieldName());
                        fieldConfig.setGroupName(request.getGroupName());
                        fieldConfig.setIsVisible(request.getIsVisible());
                        fieldMapper.insert(fieldConfig);
                    } else {
                        // 更新现有字段配置
                        fieldConfig.setFieldName(request.getFieldName());
                        fieldConfig.setGroupName(request.getGroupName());
                        fieldConfig.setIsVisible(request.getIsVisible());
                        fieldMapper.updateById(fieldConfig);
                    }
                }

                // 更新表单配置的更新时间
                formConfig.setUpdateTime(new Date());
                this.updateById(formConfig);
            }

            log.info("批量更新字段配置成功: requests.size={}", requests.size());
            return true;

        } catch (Exception e) {
            log.error("批量更新字段配置失败: requests.size={}", requests.size(), e);
            return false;
        }
    }

    @Override
    public String exportConfig(String configId, String format) {
        try {
            log.info("开始导出配置: configId={}, format={}", configId, format);

            if (StrUtil.isBlank(configId)) {
                return "导出失败：配置ID为空";
            }

            FormDisplayConfig config = this.getById(configId);
            if (config == null) {
                return "导出失败：配置不存在";
            }

            // 根据格式导出配置数据
            if ("json".equalsIgnoreCase(format)) {
                String jsonData = JSONUtil.toJsonPrettyStr(config);
                log.info("配置导出成功: configId={}, format={}", configId, format);
                return jsonData;
            } else {
                return "导出失败：不支持的格式 " + format;
            }

        } catch (Exception e) {
            log.error("导出配置失败: configId={}, format={}", configId, format, e);
            return "导出失败：" + e.getMessage();
        }
    }

    @Override
    public String importConfig(String configData, String format, String targetCenterId) {
        try {
            log.info("开始导入配置: format={}, targetCenterId={}", format, targetCenterId);

            if (StrUtil.isBlank(configData)) {
                return "导入失败：配置数据为空";
            }

            if (StrUtil.isBlank(targetCenterId)) {
                return "导入失败：目标体检中心ID为空";
            }

            // 根据格式解析配置数据
            FormDisplayConfig config;
            if ("json".equalsIgnoreCase(format)) {
                config = JSONUtil.toBean(configData, FormDisplayConfig.class);
            } else {
                return "导入失败：不支持的格式 " + format;
            }

            if (config == null) {
                return "导入失败：配置数据解析失败";
            }

            // 设置目标体检中心
            config.setCenterId(targetCenterId);
            config.setId(null); // 清除原ID，让系统自动生成新ID
            config.setCreateTime(new Date());
            config.setUpdateTime(new Date());

            // 检查是否存在相同配置
            QueryWrapper<FormDisplayConfig> wrapper = new QueryWrapper<>();
            wrapper.eq("center_id", targetCenterId)
                   .eq("form_type", config.getFormType());

            FormDisplayConfig existingConfig = this.getOne(wrapper);
            if (existingConfig != null) {
                // 更新现有配置
                BeanUtils.copyProperties(config, existingConfig, "id", "createTime", "createBy");
                existingConfig.setUpdateTime(new Date());
                this.updateById(existingConfig);

                log.info("配置导入成功（更新现有配置）: configId={}", existingConfig.getId());
                return "配置导入成功（更新现有配置）";
            } else {
                // 创建新配置
                this.save(config);

                log.info("配置导入成功（创建新配置）: configId={}", config.getId());
                return "配置导入成功（创建新配置）";
            }

        } catch (Exception e) {
            log.error("导入配置失败: format={}, targetCenterId={}", format, targetCenterId, e);
            return "导入失败：" + e.getMessage();
        }
    }

    @Override
    public Map<String, Object> getConfigStatistics(String formType, String centerId) {
        Map<String, Object> statistics = new HashMap<>();

        try {
            // 统计配置总数
            QueryWrapper<FormDisplayConfig> totalWrapper = new QueryWrapper<>();
            if (StrUtil.isNotBlank(formType)) {
                totalWrapper.eq("form_type", formType);
            }
            if (StrUtil.isNotBlank(centerId)) {
                totalWrapper.eq("center_id", centerId);
            }
            long totalCount = this.count(totalWrapper);
            statistics.put("totalConfigs", totalCount);

            // 统计启用的配置数
            QueryWrapper<FormDisplayConfig> activeWrapper = new QueryWrapper<>();
            activeWrapper.eq("is_active", true);
            if (StrUtil.isNotBlank(formType)) {
                activeWrapper.eq("form_type", formType);
            }
            if (StrUtil.isNotBlank(centerId)) {
                activeWrapper.eq("center_id", centerId);
            }
            long activeCount = this.count(activeWrapper);
            statistics.put("activeConfigs", activeCount);

            // 统计禁用的配置数
            statistics.put("inactiveConfigs", totalCount - activeCount);

            // 按表单类型统计
            List<Map<String, Object>> typeStats = this.baseMapper.selectMaps(
                new QueryWrapper<FormDisplayConfig>()
                    .select("form_type", "count(*) as count")
                    .groupBy("form_type")
            );
            statistics.put("configsByType", typeStats);

            log.info("获取配置统计信息成功: formType={}, centerId={}, statistics={}",
                    formType, centerId, statistics);

        } catch (Exception e) {
            log.error("获取配置统计信息失败: formType={}, centerId={}", formType, centerId, e);
            statistics.put("error", "获取统计信息失败: " + e.getMessage());
        }

        return statistics;
    }
}
